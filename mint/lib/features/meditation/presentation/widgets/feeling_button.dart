import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';

class FeelingButton extends StatefulWidget {
  final String feeling;
  final double process;
  final Color color;
  final Color colorLight;
  final String icon;
  final VoidCallback? onTap;
  const FeelingButton({
    super.key,
    required this.feeling,
    required this.process,
    required this.colorLight,
    required this.color,
    required this.icon,
    this.onTap,
  });

  @override
  State<FeelingButton> createState() => _FeelingButtonState();
}

class _FeelingButtonState extends State<FeelingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: widget.process,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _animationController.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _animationController.reverse();
      },
      child: Column(
        children: [
          SizedBox(
            width: 70,
            height: 70,
            child: Stack(children: [
              // Outer glow effect that appears when pressed
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: _isPressed
                      ? [
                          BoxShadow(
                            color: widget.colorLight.withOpacity(0.7),
                            blurRadius: 15,
                            spreadRadius: 5,
                          ),
                        ]
                      : [],
                ),
              ),
              // Base gradient background
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      _isPressed
                          ? widget.colorLight.withOpacity(0.6)
                          : widget.color.withOpacity(0.3),
                      _isPressed
                          ? widget.colorLight.withOpacity(0.4)
                          : widget.color.withOpacity(0.2),
                      _isPressed
                          ? widget.colorLight.withOpacity(0.2)
                          : Colors.white.withOpacity(0.1),
                    ],
                    stops: [0.4, 0.6, 1.0],
                  ),
                ),
              ),
              SizedBox.expand(
                child: AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return CircularProgressIndicator(
                      value: _progressAnimation.value,
                      color: widget.colorLight.withValues(alpha: 0.7),
                      backgroundColor: widget.color.withOpacity(0.2),
                    );
                  },
                ),
              ),
              SizedBox.expand(
                child: ColorFiltered(
                  colorFilter: ColorFilter.mode(
                    widget.colorLight,
                    BlendMode.srcIn,
                  ),
                  child: Lottie.asset(
                    'assets/lotties/orb.json',
                    fit: BoxFit.contain,
                    repeat: true,
                    animate: true,
                  ),
                ),
              ),
              Center(
                child: SvgPicture.asset(
                  widget.icon,
                  color: Colors.white.withOpacity(0.6),
                  width: 20,
                  height: 20,
                ),
              ),
            ]),
          ),
          SizedBox(height: 10),
          Text(
            widget.feeling,
            style: TextStyle(
              color: widget.colorLight,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              shadows: [
                Shadow(
                  color: Colors.white.withOpacity(0.6),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
