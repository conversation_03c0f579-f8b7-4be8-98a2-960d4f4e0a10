<svg width="400" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient cho sparkles -->
    <radialGradient id="sparkleGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1"/>
      <stop offset="70%" style="stop-color:#87CEEB;stop-opacity:0.7"/>
      <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:0"/>
    </radialGradient>

    <!-- Filter cho glow effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Sparkle shapes -->
    <g id="crossSparkle">
      <rect x="-6" y="-1" width="12" height="2" fill="url(#sparkleGradient)"/>
      <rect x="-1" y="-6" width="2" height="12" fill="url(#sparkleGradient)"/>
    </g>

    <g id="starSparkle">
      <polygon points="0,-8 2,-2 8,0 2,2 0,8 -2,2 -8,0 -2,-2" fill="url(#sparkleGradient)"/>
    </g>

    <g id="circleSparkle">
      <circle r="2" fill="url(#sparkleGradient)"/>
      <line x1="-6" y1="0" x2="6" y2="0" stroke="url(#sparkleGradient)" stroke-width="1"/>
      <line x1="0" y1="-6" x2="0" y2="6" stroke="url(#sparkleGradient)" stroke-width="1"/>
      <line x1="-4" y1="-4" x2="4" y2="4" stroke="url(#sparkleGradient)" stroke-width="1"/>
      <line x1="-4" y1="4" x2="4" y2="-4" stroke="url(#sparkleGradient)" stroke-width="1"/>
    </g>
  </defs>

  <!-- Nền trong suốt -->
  <rect width="100%" height="100%" fill="transparent"/>

  <!-- Layer 1: Sparkles chính (tốc độ 1.0x) -->
  <g filter="url(#glow)">
    <!-- Cross sparkles -->
    <use href="#crossSparkle" x="50" y="40">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,40; 450,40; -50,40" dur="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="8s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,35; 450,45; -50,35" dur="8s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="150" y="80">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,80; 450,80; -50,80" dur="8s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="8s" begin="2s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,75; 450,85; -50,75" dur="8s" begin="2s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="300" y="60">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,60; 450,60; -50,60" dur="8s" begin="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="8s" begin="4s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,55; 450,65; -50,55" dur="8s" begin="4s" repeatCount="indefinite" additive="sum"/>
    </use>

    <!-- Thêm sparkles cho layer 1 -->
    <use href="#crossSparkle" x="200" y="100">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,100; 450,100; -50,100" dur="8s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="8s" begin="1s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,95; 450,105; -50,95" dur="8s" begin="1s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="350" y="30">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,30; 450,30; -50,30" dur="8s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="8s" begin="3s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,25; 450,35; -50,25" dur="8s" begin="3s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="100" y="120">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,120; 450,120; -50,120" dur="8s" begin="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="8s" begin="5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,115; 450,125; -50,115" dur="8s" begin="5s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#crossSparkle" x="380" y="90">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,90; 450,90; -50,90" dur="8s" begin="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="8s" begin="6s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,85; 450,95; -50,85" dur="8s" begin="6s" repeatCount="indefinite" additive="sum"/>
    </use>
  </g>

  <!-- Layer 2: Sparkles chậm (tốc độ 0.7x) -->
  <g filter="url(#glow)" opacity="0.8">
    <use href="#crossSparkle" x="80" y="100">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,100; 450,100; -50,100" dur="11.4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="11.4s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,95; 450,105; -50,95" dur="11.4s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="200" y="30">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,30; 450,30; -50,30" dur="11.4s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="11.4s" begin="3s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,25; 450,35; -50,25" dur="11.4s" begin="3s" repeatCount="indefinite" additive="sum"/>
    </use>

    <!-- Thêm sparkles cho layer 2 -->
    <use href="#circleSparkle" x="320" y="110">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,110; 450,110; -50,110" dur="11.4s" begin="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="11.4s" begin="1.5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,105; 450,115; -50,105" dur="11.4s" begin="1.5s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="40" y="70">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,70; 450,70; -50,70" dur="11.4s" begin="4.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="11.4s" begin="4.5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,65; 450,75; -50,65" dur="11.4s" begin="4.5s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#crossSparkle" x="260" y="50">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,50; 450,50; -50,50" dur="11.4s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="11.4s" begin="2s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,45; 450,55; -50,45" dur="11.4s" begin="2s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="140" y="130">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,130; 450,130; -50,130" dur="11.4s" begin="5.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="11.4s" begin="5.5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,125; 450,135; -50,125" dur="11.4s" begin="5.5s" repeatCount="indefinite" additive="sum"/>
    </use>
  </g>

  <!-- Layer 3: Sparkles nhanh (tốc độ 1.3x) -->
  <g filter="url(#glow)" opacity="0.9">
    <use href="#circleSparkle" x="120" y="120">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,120; 450,120; -50,120" dur="6.2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="6.2s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,115; 450,125; -50,115" dur="6.2s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#crossSparkle" x="280" y="90">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,90; 450,90; -50,90" dur="6.2s" begin="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="6.2s" begin="1.5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,85; 450,95; -50,85" dur="6.2s" begin="1.5s" repeatCount="indefinite" additive="sum"/>
    </use>

    <!-- Thêm sparkles cho layer 3 -->
    <use href="#starSparkle" x="60" y="45">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,45; 450,45; -50,45" dur="6.2s" begin="0.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="6.2s" begin="0.8s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,40; 450,50; -50,40" dur="6.2s" begin="0.8s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="220" y="25">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,25; 450,25; -50,25" dur="6.2s" begin="2.3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="6.2s" begin="2.3s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,20; 450,30; -50,20" dur="6.2s" begin="2.3s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#crossSparkle" x="360" y="135">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,135; 450,135; -50,135" dur="6.2s" begin="3.1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="6.2s" begin="3.1s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,130; 450,140; -50,130" dur="6.2s" begin="3.1s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="180" y="105">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,105; 450,105; -50,105" dur="6.2s" begin="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="6.2s" begin="4s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,100; 450,110; -50,100" dur="6.2s" begin="4s" repeatCount="indefinite" additive="sum"/>
    </use>
  </g>

  <!-- Layer 4: Sparkles rất chậm (tốc độ 0.5x) -->
  <g filter="url(#glow)" opacity="0.7">
    <use href="#starSparkle" x="180" y="110">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,110; 450,110; -50,110" dur="16s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="16s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,105; 450,115; -50,105" dur="16s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="350" y="50">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,50; 450,50; -50,50" dur="16s" begin="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;1;0" dur="16s" begin="5s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,45; 450,55; -50,45" dur="16s" begin="5s" repeatCount="indefinite" additive="sum"/>
    </use>

    <!-- Thêm sparkles cho layer 4 -->
    <use href="#crossSparkle" x="90" y="80">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,80; 450,80; -50,80" dur="16s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="16s" begin="2s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,75; 450,85; -50,75" dur="16s" begin="2s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#starSparkle" x="270" y="35">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,35; 450,35; -50,35" dur="16s" begin="8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="16s" begin="8s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,30; 450,40; -50,30" dur="16s" begin="8s" repeatCount="indefinite" additive="sum"/>
    </use>

    <use href="#circleSparkle" x="30" y="125">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,125; 450,125; -50,125" dur="16s" begin="12s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="16s" begin="12s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" type="translate"
                        values="-50,120; 450,130; -50,120" dur="16s" begin="12s" repeatCount="indefinite" additive="sum"/>
    </use>
  </g>

  <!-- Layer 5: Sparkles siêu nhỏ (tốc độ 1.8x) -->
  <g filter="url(#glow)" opacity="0.5">
    <circle r="0.5" fill="#E0F6FF" cx="45" cy="55">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="4.4s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.8" fill="#E0F6FF" cx="125" cy="85">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="4.4s" begin="1s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.6" fill="#E0F6FF" cx="205" cy="65">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="4.4s" begin="2s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.7" fill="#E0F6FF" cx="285" cy="115">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="4.4s" begin="3s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.5" fill="#E0F6FF" cx="365" cy="75">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="0.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0.6;0" dur="4.4s" begin="0.5s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.9" fill="#E0F6FF" cx="75" cy="105">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="1.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="4.4s" begin="1.5s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.4" fill="#E0F6FF" cx="155" cy="35">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0.5;0" dur="4.4s" begin="2.5s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.6" fill="#E0F6FF" cx="235" cy="135">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="3.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="4.4s" begin="3.5s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.8" fill="#E0F6FF" cx="315" cy="95">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="4.4s" begin="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="4.4s" begin="4s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Thêm sparkles nhỏ để tạo mật độ cao hơn -->
  <g filter="url(#glow)" opacity="0.6">
    <!-- Sparkles nhỏ layer 1 -->
    <circle r="1" fill="#87CEEB" cx="70" cy="70">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="9s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="9s" repeatCount="indefinite"/>
    </circle>

    <circle r="1.5" fill="#87CEEB" cx="220" cy="45">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="7s" begin="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="7s" begin="2s" repeatCount="indefinite"/>
    </circle>

    <circle r="1" fill="#87CEEB" cx="320" cy="130">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="12s" begin="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="12s" begin="4s" repeatCount="indefinite"/>
    </circle>

    <!-- Sparkles nhỏ layer 2 -->
    <circle r="0.8" fill="#B0E0E6" cx="40" cy="95">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="10s" begin="1s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.6;0.6;0" dur="10s" begin="1s" repeatCount="indefinite"/>
    </circle>

    <circle r="1.2" fill="#B0E0E6" cx="160" cy="25">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="8.5s" begin="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="8.5s" begin="3s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.9" fill="#B0E0E6" cx="290" cy="105">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="11s" begin="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.7;0.7;0" dur="11s" begin="5s" repeatCount="indefinite"/>
    </circle>

    <!-- Sparkles nhỏ layer 3 -->
    <circle r="1.3" fill="#ADD8E6" cx="110" cy="55">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="6.8s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.9;0.9;0" dur="6.8s" repeatCount="indefinite"/>
    </circle>

    <circle r="0.7" fill="#ADD8E6" cx="250" cy="85">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="13s" begin="2.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.5;0.5;0" dur="13s" begin="2.5s" repeatCount="indefinite"/>
    </circle>

    <circle r="1.1" fill="#ADD8E6" cx="380" cy="75">
      <animateTransform attributeName="transform" type="translate"
                        values="-50,0; 450,0; -50,0" dur="9.5s" begin="4.5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;0.8;0.8;0" dur="9.5s" begin="4.5s" repeatCount="indefinite"/>
    </circle>
  </g>


</svg>
