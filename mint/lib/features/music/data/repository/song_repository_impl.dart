import 'package:mental_health/features/music/data/datasources/song_remote_data_source.dart';
import 'package:mental_health/features/music/domain/entities/song.dart';
import 'package:mental_health/features/music/domain/repository/song_repository.dart';

class SongRepositoryImpl implements SongRepository {
  final SongRemoteDataSource remoteDataSource;

  SongRepositoryImpl({required this.remoteDataSource});

  @override
  Future<List<Song>> getAllSongs() async {
    final songModels = await remoteDataSource.getAllSongs();
    return songModels;
  }
}
