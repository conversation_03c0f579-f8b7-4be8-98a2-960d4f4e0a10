import '../domain/entities/task.dart';
import '../domain/entities/task_category.dart';

/// Demo data cho Tasks feature
class TaskDemoData {
  static List<Task> getDemoTasks() {
    final categories = TaskCategory.defaultCategories;
    final now = DateTime.now();
    
    return [
      Task(
        id: '1',
        title: 'Thiền chánh niệm 10 phút',
        description: 'Thực hành thiền chánh niệm để bắt đầu ngày mới tích cực',
        category: categories.firstWhere((c) => c.id == 'spiritual'),
        isCompleted: true,
        createdAt: now.subtract(const Duration(hours: 2)),
        completedAt: now.subtract(const Duration(hours: 1)),
        priority: 3,
        estimatedMinutes: 10,
        tags: ['thiền', 'sáng', 'chánh niệm'],
        notes: 'Tập trung vào hơi thở và cảm nhận hiện tại',
      ),
      Task(
        id: '2',
        title: '<PERSON>i<PERSON><PERSON> nhật ký cảm xúc',
        description: '<PERSON><PERSON> lạ<PERSON> những cảm xúc và suy nghĩ trong ngày',
        category: categories.firstWhere((c) => c.id == 'emotional'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(hours: 1)),
        priority: 2,
        dueDate: now.add(const Duration(hours: 6)),
        estimatedMinutes: 15,
        tags: ['nhật ký', 'cảm xúc', 'tự nhận thức'],
        notes: 'Viết ít nhất 3 điều tích cực trong ngày',
      ),
      Task(
        id: '3',
        title: 'Tập yoga 30 phút',
        description: 'Thực hiện các bài tập yoga để thư giãn cơ thể và tinh thần',
        category: categories.firstWhere((c) => c.id == 'physical'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(minutes: 30)),
        priority: 2,
        dueDate: now.add(const Duration(hours: 3)),
        estimatedMinutes: 30,
        tags: ['yoga', 'thể dục', 'thư giãn'],
        notes: 'Tập trung vào các tư thế cơ bản',
      ),
      Task(
        id: '4',
        title: 'Đọc sách phát triển bản thân',
        description: 'Đọc ít nhất 20 trang sách về tâm lý học tích cực',
        category: categories.firstWhere((c) => c.id == 'learning'),
        isCompleted: true,
        createdAt: now.subtract(const Duration(hours: 3)),
        completedAt: now.subtract(const Duration(hours: 2)),
        priority: 1,
        estimatedMinutes: 25,
        tags: ['đọc sách', 'học tập', 'phát triển'],
        notes: 'Cuốn "Tâm lý học hạnh phúc" rất hay',
      ),
      Task(
        id: '5',
        title: 'Gọi điện cho gia đình',
        description: 'Liên lạc với gia đình để chia sẻ và kết nối',
        category: categories.firstWhere((c) => c.id == 'social'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(minutes: 15)),
        priority: 2,
        dueDate: now.add(const Duration(hours: 8)),
        estimatedMinutes: 20,
        tags: ['gia đình', 'kết nối', 'chia sẻ'],
        notes: 'Hỏi thăm sức khỏe và tâm trạng của mọi người',
      ),
      Task(
        id: '6',
        title: 'Hoàn thành báo cáo công việc',
        description: 'Viết báo cáo tiến độ dự án tuần này',
        category: categories.firstWhere((c) => c.id == 'work'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(hours: 4)),
        priority: 3,
        dueDate: now.add(const Duration(hours: 2)),
        estimatedMinutes: 60,
        tags: ['công việc', 'báo cáo', 'deadline'],
        notes: 'Cần hoàn thành trước 5h chiều',
      ),
      Task(
        id: '7',
        title: 'Dọn dẹp phòng ngủ',
        description: 'Sắp xếp lại đồ đạc và tạo không gian sống thoải mái',
        category: categories.firstWhere((c) => c.id == 'personal'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(hours: 5)),
        priority: 1,
        estimatedMinutes: 45,
        tags: ['dọn dẹp', 'tổ chức', 'không gian'],
        notes: 'Tập trung vào việc sắp xếp tủ quần áo',
      ),
      Task(
        id: '8',
        title: 'Uống đủ 8 ly nước',
        description: 'Duy trì việc uống nước đầy đủ trong ngày',
        category: categories.firstWhere((c) => c.id == 'health'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(hours: 6)),
        priority: 2,
        estimatedMinutes: 5,
        tags: ['sức khỏe', 'nước', 'thói quen'],
        notes: 'Đã uống 4 ly, còn 4 ly nữa',
      ),
      Task(
        id: '9',
        title: 'Lập kế hoạch tuần tới',
        description: 'Lên kế hoạch chi tiết cho các hoạt động tuần sau',
        category: categories.firstWhere((c) => c.id == 'personal'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(minutes: 45)),
        priority: 2,
        dueDate: now.add(const Duration(days: 1)),
        estimatedMinutes: 30,
        tags: ['kế hoạch', 'tổ chức', 'tuần tới'],
        notes: 'Bao gồm cả công việc và hoạt động cá nhân',
      ),
      Task(
        id: '10',
        title: 'Thực hành biết ơn',
        description: 'Viết ra 3 điều biết ơn trong ngày hôm nay',
        category: categories.firstWhere((c) => c.id == 'emotional'),
        isCompleted: false,
        createdAt: now.subtract(const Duration(minutes: 10)),
        priority: 1,
        estimatedMinutes: 10,
        tags: ['biết ơn', 'tích cực', 'hạnh phúc'],
        notes: 'Tập trung vào những điều nhỏ nhặt trong cuộc sống',
      ),
    ];
  }
}
